package shanks.scgl.service.v3;

import shanks.scgl.bean.api.account.LaunchModel;
import shanks.scgl.bean.api.base.ResponseModel;
import shanks.scgl.bean.db.IdentityCode;
import shanks.scgl.bean.db.Launch;
import shanks.scgl.bean.db.User;
import shanks.scgl.bean.v3.api.account.*;
import shanks.scgl.bean.v3.card.AccountCard;
import shanks.scgl.factory.AdminFactory;
import shanks.scgl.factory.Notify3rdFactory;
import shanks.scgl.factory.Party3Factory;
import shanks.scgl.factory.UserFactory;
import shanks.scgl.service.BaseService;
import shanks.scgl.utils.ICodeUtil;
import shanks.scgl.utils.TextUtil;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户相关服务类，处理用户注册和账户管理
 * API版本：v3
 */
@Path("/v3/account")
public class AccountService extends BaseService {

    /**
     * 用户注册接口
     * 支持QQ和微信第三方平台注册
     *
     * @param model 注册请求模型，包含注册类型和授权码
     * @return 注册结果，成功则返回账户信息，失败则返回错误提示
     */
    @POST
    @Path("/register")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> register(RegisterModel model) {
        // 参数校验
        if (!RegisterModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }

        // 初始化账户信息
        String account;
        String qqId = null;
        String wxId = null;

        try {
            // 根据注册类型进行不同处理
            if (model.getType() == RegisterModel.TYPE_QQ) {
                // QQ注册流程
                User user = UserFactory.findByQQ(model.getCode());
                if (user != null) {
                    // QQ账号已被注册，提示直接登录
                    return ResponseModel.buildError("QQ已经注册，请直接登录");
                } else {
                    // 这里code就是QQ的ID
                    qqId = model.getCode();

                    // 生成账户
                    account = "QQ" + TextUtil.getMD5(qqId + LocalDateTime.now());
                }
            } else if (model.getType() == RegisterModel.TYPE_WX) {
                // 微信注册流程
                wxId = Party3Factory.getWxOpenID(model.getCode());
                if (wxId == null) {
                    // 获取微信OpenID失败
                    return ResponseModel.buildError("获取微信信息失败，请重试");
                }

                User user = UserFactory.findByWX(wxId);
                if (user != null) {
                    // 微信账号已被注册，提示直接登录
                    return ResponseModel.buildError("微信已经注册，请直接登录");
                } else {
                    // 生成账户
                    account = "WX" + TextUtil.getMD5(wxId + LocalDateTime.now());
                }
            } else {
                // 不支持的注册类型
                return ResponseModel.buildError("注册类型异常");
            }

            // 创建用户账号
            User user = UserFactory.registerV3(account, qqId, wxId);

            if (user != null) {
                try {
                    // 创建初始权限和设置
                    AdminFactory.createOriginAnth(user, 1);
                    AdminFactory.createKeepsAnth(user, 1);
                    AdminFactory.sendWelcome(user);

                    // 更新账号为更简洁的格式：前缀+用户ID
                    if (model.getType() == RegisterModel.TYPE_QQ) {
                        user.setAccount("QQ" + user.getId());
                    } else if (model.getType() == RegisterModel.TYPE_WX) {
                        user.setAccount("WX" + user.getId());
                    }
                    user = UserFactory.update(user);
                } catch (Exception e) {
                    // 记录错误但不影响注册流程
                    // 实际项目中应该添加日志记录: Logger.error("用户初始化设置失败", e);
                }

                // 构建并返回用户信息
                AccountCard rspModel = new AccountCard(user);
                return ResponseModel.buildSuccess("注册成功", rspModel);
            } else {
                return ResponseModel.buildError("注册失败");
            }
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("注册过程发生异常", e);
            return ResponseModel.buildError("注册过程发生异常，请稍后重试");
        }
    }

    @POST
    @Path("/login3rd")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> login(Login3rdModel model) {
        if (!Login3rdModel.check(model)) {
            //参数异常
            return ResponseModel.buildError("参数异常");
        }

        User user = null;
        //QQ登录
        if (model.getType() == Login3rdModel.TYPE_QQ) {
            user = UserFactory.findByQQ(model.getCode());
            if (user == null) {
                return ResponseModel.buildError("QQ未绑定");
            }
        } else if (model.getType() == Login3rdModel.TYPE_WX) {
            String wxId = Party3Factory.getWxOpenID(model.getCode());
            user = UserFactory.findByWX(wxId);
            if (user == null) {
                return ResponseModel.buildError("微信未绑定");
            }
        }

        //判断异常情况
        if (user == null) {
            return ResponseModel.buildError("登录失败");
        }
        user = UserFactory.login(user);
        return ResponseModel.buildSuccess("登录成功", new AccountCard(user));
    }

    /**
     * 传统登录接口
     * 支持账号+密码、手机号+密码、手机号+验证码三种登录方式
     *
     * @param model 登录请求模型，包含登录类型和登录凭证
     * @return 登录结果，成功则返回账户信息，失败则返回错误提示
     */
    @POST
    @Path("/loginOld")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> login(LoginOldModel model) {
        // 参数校验
        if (!LoginOldModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }
        
        User user;
        
        try {
            // 根据登录类型进行不同处理
            if (model.getType() == LoginOldModel.TYPE_ACCOUNT_PWD) {
                // 账号+密码登录
                String account = model.getId().trim();
                String password = model.getPwd().trim();
                
                // 尝试直接登录
                user = UserFactory.login(account, password, false);
                if (user == null) {
                    return ResponseModel.buildError("账号或密码错误");
                }
            } else if (model.getType() == LoginOldModel.TYPE_PHONE_PWD) {
                // 手机号+密码登录
                String phone = model.getId().trim();
                String password = model.getPwd().trim();
                
                // 先查找用户
                user = UserFactory.findByPhone(phone);
                if (user == null) {
                    return ResponseModel.buildError("手机号未绑定");
                }
                
                // 使用账号和密码登录
                user = UserFactory.login(user.getAccount(), password, false);
                if (user == null) {
                    return ResponseModel.buildError("账号或密码错误");
                }
            } else if (model.getType() == LoginOldModel.TYPE_PHONE_CODE) {
                // 手机号+验证码登录
                String phone = model.getId().trim();
                String code = model.getPwd().trim();
                
                // 先查找用户
                user = UserFactory.findByPhone(phone);
                if (user == null) {
                    return ResponseModel.buildError("手机号未绑定");
                }
                
                // 验证短信验证码
                IdentityCode identityCode = Notify3rdFactory.findByIdentify(phone);
                if (!ICodeUtil.checkIdentifyCode(identityCode, code)) {
                    return ResponseModel.buildError("验证码错误或已过期");
                }
                
                // 验证通过，更新登录状态
                user = UserFactory.login(user);
            } else {
                return ResponseModel.buildError("不支持的登录类型");
            }
            
            // 登录成功，返回用户信息
            if (user != null) {
                AccountCard rspModel = new AccountCard(user);
                return ResponseModel.buildSuccess("登录成功", rspModel);
            } else {
                return ResponseModel.buildError("登录失败");
            }
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("登录过程发生异常", e);
            return ResponseModel.buildError("登录过程发生异常，请稍后重试");
        }
    }
    
    /**
     * 获取手机验证码接口
     * 支持登录验证和绑定手机号两种场景
     *
     * @param token 用户令牌（绑定场景必须，登录场景可选）
     * @param model 请求模型，包含手机号和使用场景类型
     * @return 成功返回空，失败返回错误提示
     */
    @POST
    @Path("/sendSms")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<Void> sendSms(@HeaderParam("token") String token, SendSmsModel model) {
        // 参数校验
        if (!SendSmsModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }
        
        try {
            String phone = model.getPhone().trim();
            int type = model.getType(); // 新增type字段: 1-登录验证, 2-绑定手机号
            
            // 登录场景：验证手机号是否已绑定
            if (type == SendSmsModel.TYPE_LOGIN) {
                User user = UserFactory.findByPhone(phone);
                if (user == null) {
                    return ResponseModel.buildError("手机号未绑定");
                }
            } 
            // 绑定场景：验证用户是否已登录，手机号是否未被绑定
            else if (type == SendSmsModel.TYPE_BIND) {
                User self = UserFactory.findByToken(token);
                if (self == null) {
                    return ResponseModel.buildError("请先登录");
                }
                
                // 检查手机号是否已被其他账号绑定
                User existUser = UserFactory.findByPhone(phone);
                if (existUser != null && existUser.getId() != self.getId()) {
                    return ResponseModel.buildError("该手机号已被其他账号绑定");
                }
            } else {
                return ResponseModel.buildError("不支持的验证码类型");
            }
            
            // 检查验证码发送频率
            if (!ICodeUtil.checkFrequent(phone)) {
                return ResponseModel.buildError("验证码发送过于频繁，请稍后再试");
            }
            
            // 发送验证码，根据不同场景使用不同的验证码类型
            int identityCodeType = (type == SendSmsModel.TYPE_LOGIN) ?
                    IdentityCode.TYPE_LOGIN : IdentityCode.TYPE_BIND;
            IdentityCode identityCode = Notify3rdFactory.sendIcSms(phone, identityCodeType);
            
            if (identityCode != null) {
                return ResponseModel.buildSuccess("验证码发送成功");
            } else {
                return ResponseModel.buildError("验证码发送失败，请稍后再试");
            }
        } catch (Exception e) {
            return ResponseModel.buildError("验证码发送过程发生异常，请稍后重试");
        }
    }

    /**
     * 绑定第三方账号接口
     * 支持将QQ或微信绑定到已有账号
     *
     * @param token 用户令牌
     * @param model 绑定模型，包含绑定类型和授权码
     * @return 绑定结果，成功则返回更新后的账户信息，失败则返回错误提示
     */
    @PUT
    @Path("/bind3rd")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> bind3rd(@HeaderParam("token") String token, Bind3rdModel model) {
        // 参数校验
        if (!Bind3rdModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }
        
        // 检查用户是否已登录
        User self = UserFactory.findByToken(token);
        if (self == null) {
            return ResponseModel.buildError("请先登录");
        }
        
        try {
            // 根据绑定类型进行不同处理
            if (model.getType() == Bind3rdModel.TYPE_QQ) {
                // QQ绑定流程
                String qqId = model.getCode().trim();
                
                // 检查QQ是否已被其他账号绑定
                User existUser = UserFactory.findByQQ(qqId);
                if (existUser != null && existUser.getId() != self.getId()) {
                    return ResponseModel.buildError("该QQ已被其他账号绑定");
                }
                
                // 已经绑定过相同的QQ
                if (existUser != null && existUser.getId() == self.getId()) {
                    return ResponseModel.buildSuccess("该QQ已绑定到当前账号", new AccountCard(self));
                }
                
                // 绑定QQ
                self = UserFactory.bindQQ(self, qqId);
                if (self != null) {
                    return ResponseModel.buildSuccess("QQ绑定成功", new AccountCard(self));
                } else {
                    return ResponseModel.buildError("QQ绑定失败");
                }
            } else if (model.getType() == Bind3rdModel.TYPE_WX) {
                // 微信绑定流程
                String wxOpenId = Party3Factory.getWxOpenID(model.getCode());
                if (wxOpenId == null) {
                    return ResponseModel.buildError("获取微信信息失败，请重试");
                }
                
                // 检查微信是否已被其他账号绑定
                User existUser = UserFactory.findByWX(wxOpenId);
                if (existUser != null && existUser.getId() != self.getId()) {
                    return ResponseModel.buildError("该微信已被其他账号绑定");
                }
                
                // 已经绑定过相同的微信
                if (existUser != null && existUser.getId() == self.getId()) {
                    return ResponseModel.buildSuccess("该微信已绑定到当前账号", new AccountCard(self));
                }
                
                // 绑定微信
                self = UserFactory.bindWX(self, wxOpenId);
                if (self != null) {
                    return ResponseModel.buildSuccess("微信绑定成功", new AccountCard(self));
                } else {
                    return ResponseModel.buildError("微信绑定失败");
                }
            } else {
                return ResponseModel.buildError("不支持的绑定类型");
            }
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("绑定第三方账号过程发生异常", e);
            return ResponseModel.buildError("绑定过程发生异常，请稍后重试");
        }
    }

    /**
     * 解绑账号接口
     * 支持解绑QQ、微信或手机号
     *
     * @param token 用户令牌
     * @param type 解绑类型: "qq"、"wx"或"phone"
     * @return 解绑结果，成功则返回更新后的账户信息，失败则返回错误提示
     */
    @DELETE
    @Path("/bind/{type}")
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> unbind(@HeaderParam("token") String token, 
                                           @PathParam("type") String type) {
        // 检查用户是否已登录
        User self = UserFactory.findByToken(token);
        if (self == null) {
            return ResponseModel.buildError("请先登录");
        }
        
        try {
            // 根据解绑类型进行不同处理
            switch (type.toLowerCase()) {
                case "qq":
                    // 检查是否已绑定QQ
                    if (self.getQqId() == null) {
                        return ResponseModel.buildError("QQ未绑定");
                    }
                    
                    // 确保有其他登录方式
                    if (self.getWxId() == null && self.getPhone() == null) {
                        return ResponseModel.buildError("必须保留至少一种绑定方式");
                    }
                    
                    // 解绑QQ
                    self.setQqId(null);
                    self = UserFactory.update(self);
                    return ResponseModel.buildSuccess("QQ解绑成功", new AccountCard(self));
                    
                case "wx":
                    // 检查是否已绑定微信
                    if (self.getWxId() == null) {
                        return ResponseModel.buildError("微信未绑定");
                    }
                    
                    // 确保有其他登录方式
                    if (self.getQqId() == null && self.getPhone() == null) {
                        return ResponseModel.buildError("必须保留至少一种绑定方式");
                    }
                    
                    // 解绑微信
                    self.setWxId(null);
                    self = UserFactory.update(self);
                    return ResponseModel.buildSuccess("微信解绑成功", new AccountCard(self));
                    
                case "phone":
                    // 检查是否已绑定手机号
                    if (self.getPhone() == null) {
                        return ResponseModel.buildError("手机号未绑定");
                    }
                    
                    // 确保有其他登录方式
                    if (self.getQqId() == null && self.getWxId() == null) {
                        return ResponseModel.buildError("必须保留至少一种绑定方式");
                    }
                                                   
                    // 解绑手机号
                    self.setPhone(null);
                    self = UserFactory.update(self);
                    return ResponseModel.buildSuccess("手机号解绑成功", new AccountCard(self));
                    
                default:
                    return ResponseModel.buildError("不支持的解绑类型");
            }
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("解绑过程发生异常", e);
            return ResponseModel.buildError("解绑过程发生异常，请稍后重试");
        }
    }

    @PUT
    @Path("/bindPhone")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> bindPhone(@HeaderParam("token") String token, 
                                               BindPhoneModel model) {
        // 参数校验
        if (!BindPhoneModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }
        
        // 检查用户是否已登录
        User self = UserFactory.findByToken(token);
        if (self == null) {
            return ResponseModel.buildError("请先登录");
        }
        
        try {
            String phone = model.getPhone().trim();
            String code = model.getCode().trim();
            
            // 检查手机号是否已被其他账号绑定
            User existUser = UserFactory.findByPhone(phone);
            if (existUser != null && existUser.getId() != self.getId()) {
                return ResponseModel.buildError("该手机号已被其他账号绑定");
            }
            
            // 验证短信验证码
            IdentityCode identityCode = Notify3rdFactory.findByIdentify(phone);
            if (!ICodeUtil.checkIdentifyCode(identityCode, code)) {
                return ResponseModel.buildError("验证码错误或已过期");
            }
            
            // 绑定手机号
            self = UserFactory.bindPhone(self, phone);
            if (self != null) {
                return ResponseModel.buildSuccess("手机号绑定成功", new AccountCard(self));
            } else {
                return ResponseModel.buildError("手机号绑定失败");
            }
        } catch (Exception e) {
            return ResponseModel.buildError("绑定过程发生异常，请稍后重试");
        }
    }

    @PUT
    @Path("/password/{pwd}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> changePwd(@HeaderParam("token") String token,@PathParam("pwd") String pwd) {
        // 检查用户是否已登录
        User self = UserFactory.findByToken(token);
        if (self == null) {
            return ResponseModel.buildError("请先登录");
        }

        if (UserFactory.updatePwd(self, pwd) != null) {
            return ResponseModel.buildSuccess("密码修改成功", new AccountCard(self));
        } else {
            return ResponseModel.buildError("密码修改失败");
        }
    }

    /**
     * 注销账号接口
     * 将账号改为锁定状态，用户名重置为编号信息，并删除所有社交关系
     *
     * @param token 用户令牌
     * @return 注销结果
     */
    @DELETE
    @Path("/register/{pwd}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> unRegister(@HeaderParam("token") String token, @PathParam("pwd") String pwd) {
        // 检查用户是否已登录
        User self = UserFactory.findByToken(token);
        if (self == null) {
            return ResponseModel.buildError("请先登录");
        }
        
        try {
            // 验证密码确认
            if (!UserFactory.checkPassword(self, pwd)) {
                return ResponseModel.buildError("密码错误");
            }
            
            // 1. 解除所有关注关系
            List<Integer> followIds = UserFactory.getFollowIds(self.getId());
            
            for (Integer followId : followIds) {
                User followUser = UserFactory.findById(followId);
                if (followUser != null) {
                    UserFactory.unFollow(self, followUser);
                }
            }
            
            // 2. 解除所有粉丝关系 (获取所有关注了自己的用户)
            List<Integer> fansIds = UserFactory.getFollowIds(self.getId());
            
            for (Integer fansId : fansIds) {
                User fansUser = UserFactory.findById(fansId);
                if (fansUser != null) {
                    UserFactory.unFollow(fansUser, self);
                }
            }
            
            // 3. 重置用户名为编号信息
            self.loadInfo().setUserName("DELETED" + self.getId());
            self.setAccount("DELETED" + self.getId());
            
            // 4. 将账户设置为锁定状态
            self.setLocked(1);
            
            // 5. 清除登录凭证
            self.setToken(null);

            //6. 清除所有绑定关系
            self.setQqId(null);
            self.setWxId(null);
            self.setPhone(null);
            self.setPushId(null);
            
            // 7. 更新用户信息
            UserFactory.update(self);
            
            return ResponseModel.buildSuccess("账号已注销");
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("注销账号过程发生异常", e);
            e.printStackTrace();
            return ResponseModel.buildError("注销过程发生异常，请稍后重试");
        }
    }

    /**
     * 应用启动接口
     * 记录设备启动信息，如果提供了token则返回用户信息
     *
     * @param model 启动请求模型，包含设备标识和应用信息
     * @return 启动结果，如果用户已登录则返回账户信息
     */
    @PUT
    @Path("/launch")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseModel<AccountCard> launch(LaunchModel model) {
        // 参数校验
        if (!LaunchModel.check(model)) {
            return ResponseModel.buildError("参数异常");
        }
        
        try {
            // 获取用户信息（如果已登录）
            User user = null;
            if (model.getToken() != null && !model.getToken().isEmpty()) {
                user = UserFactory.findByToken(model.getToken());
            }
            
            // 记录启动信息
            Launch launch = UserFactory.findLaunch(model.getId());
            if (launch == null) {
                launch = new Launch(user, model);
            } else {
                launch.update(user, model);
            }
            UserFactory.saveLaunch(launch);
            
            // 如果用户已登录，返回用户信息
            if (user != null) {
                return ResponseModel.buildSuccess("启动成功", new AccountCard(user));
            } else {
                return ResponseModel.buildSuccess("启动成功");
            }
        } catch (Exception e) {
            // 捕获所有未处理的异常
            // 实际项目中应该添加日志记录: Logger.error("应用启动过程发生异常", e);
            return ResponseModel.buildError("启动过程发生异常，请稍后重试");
        }
    }

}
